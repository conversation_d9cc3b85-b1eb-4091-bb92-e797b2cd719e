import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { ViewMode } from '../types/chat';
import { VIEW_MODE } from '../types/chat';

// Simplified Message Interface
interface SimpleMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
  images?: string[]; // Optional array of base64-encoded images
  isStreaming?: boolean; // Whether this message is currently being streamed
  isComplete?: boolean; // Whether streaming is complete for this message
  toolInfo?: string; // Information about tool calls during streaming
}

// Simplified Chat Store Interface
interface ChatStore {
  // State
  messages: SimpleMessage[];
  isTyping: boolean;
  viewMode: ViewMode;
  unreadCount: number;
  currentThreadId: string | null; // Current conversation thread ID
  isChatboxExpanded: boolean; // Whether chatbox is in expanded mode
  streamingEnabled: boolean; // Whether streaming is enabled
  currentStreamingMessageId: string | null; // ID of message currently being streamed
  currentAgent: string | null; // Current active agent ID

  // Actions
  addMessage: (content: string, sender: 'user' | 'agent', images?: string[]) => void;
  updateMessage: (id: string, updates: Partial<SimpleMessage>) => void; // Update specific message
  clearMessages: () => void;
  setTyping: (isTyping: boolean, agentId?: string) => void;
  setViewMode: (mode: ViewMode) => void;
  markAsRead: () => void;
  setThreadId: (threadId: string | null) => void;
  clearConversation: () => void; // Clear messages and reset thread ID
  toggleChatboxExpanded: () => void; // Toggle chatbox expanded state
  setChatboxExpanded: (expanded: boolean) => void; // Set chatbox expanded state
  setStreamingEnabled: (enabled: boolean) => void; // Enable/disable streaming
  setCurrentStreamingMessageId: (id: string | null) => void; // Set current streaming message ID
  setCurrentAgent: (agentId: string | null) => void; // Set current active agent
}

// Generate unique ID for messages
const generateMessageId = (): string => {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Create the simplified chat store
export const useChatStore = create<ChatStore>()(
  devtools(
    (set) => ({
        // Initial state
        messages: [],
        isTyping: false,
        viewMode: VIEW_MODE.MINIMIZED,
        unreadCount: 0,
        currentThreadId: null,
        isChatboxExpanded: false,
        streamingEnabled: true, 
        currentStreamingMessageId: null,
        currentAgent: null,

        // Actions
        addMessage: (content: string, sender: 'user' | 'agent', images?: string[]) => {
          const message: SimpleMessage = {
            id: generateMessageId(),
            content,
            sender,
            timestamp: new Date(),
            ...(images && images.length > 0 && { images }),
          };

          // console.log('Adding message to store:', { content, sender });

          set((state) => ({
            messages: [...state.messages, message],
            // Increment unread count if it's an agent message and chat is minimized
            unreadCount:
              sender === 'agent' && state.viewMode === VIEW_MODE.MINIMIZED
                ? state.unreadCount + 1
                : state.unreadCount,
          }));
        },

        updateMessage: (id: string, updates: Partial<SimpleMessage>) => {
          set((state) => ({
            messages: state.messages.map(msg =>
              msg.id === id ? { ...msg, ...updates } : msg
            ),
          }));
        },

        clearMessages: () => {
          set({ messages: [], unreadCount: 0 });
        },

        setTyping: (isTyping, agentId) => {
          set({
            isTyping,
            currentAgent: isTyping ? agentId || null : null
          });
        },

        setViewMode: (viewMode) => {
          set((state) => ({
            viewMode,
            // Clear unread count when opening chat (not minimized)
            unreadCount: viewMode === VIEW_MODE.MINIMIZED ? state.unreadCount : 0,
          }));
        },

        markAsRead: () => {
          set({ unreadCount: 0 });
        },

        setThreadId: (threadId) => {
          console.log('Setting threadId:', threadId);
          debugger
          set({ currentThreadId: threadId });
        },

        clearConversation: () => {
          console.log('Clearing conversation and threadId');
          debugger
          set({
            messages: [],
            unreadCount: 0,
            currentThreadId: null,
            isTyping: false,
            currentAgent: null
          });
        },

        toggleChatboxExpanded: () => {
          set((state) => ({
            isChatboxExpanded: !state.isChatboxExpanded
          }));
        },

        setChatboxExpanded: (expanded) => {
          set({ isChatboxExpanded: expanded });
        },

        setStreamingEnabled: (enabled) => {
          set({ streamingEnabled: enabled });
        },

        setCurrentStreamingMessageId: (id) => {
          set({ currentStreamingMessageId: id });
        },

        setCurrentAgent: (agentId) => {
          set({ currentAgent: agentId });
        },
      }),
    {
      name: 'chat-store',
    }
  )
);

// Simplified selectors
export const useChatMessages = () => useChatStore((state) => state.messages);
export const useChatTyping = () => useChatStore((state) => state.isTyping);
export const useChatViewMode = () => useChatStore((state) => state.viewMode);
export const useChatUnreadCount = () => useChatStore((state) => state.unreadCount);
export const useChatThreadId = () => useChatStore((state) => state.currentThreadId);
